"use client"

import { useState, useEffect } from "react"
import { cn } from "@/lib/utils"
import { ChevronUp } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { createSupabaseClient } from "@/lib/supabase/client"

interface VoteProps {
  productId: number
  initialUpvotes?: number
  className?: string
}

export function Vote({
  productId,
  initialUpvotes = 0,
  className,
}: VoteProps) {
  const [upvotes, setUpvotes] = useState(initialUpvotes)
  const [hasVoted, setHasVoted] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [isLoggedIn, setIsLoggedIn] = useState(false)
  const { toast } = useToast()

  // 组件加载时获取最新的点赞数和用户登录状态
  useEffect(() => {
    const fetchData = async () => {
      try {
        const supabase = createSupabaseClient()

        // 获取产品的点赞数
        const { data: votesData } = await supabase
          .from('product_votes')
          .select('upvotes')
          .eq('product_id', productId)
          .maybeSingle()

        if (votesData) {
          setUpvotes(votesData.upvotes)
        }

        // 检查用户是否已登录
        const { data: { session } } = await supabase.auth.getSession()
        const isUserLoggedIn = !!session
        setIsLoggedIn(isUserLoggedIn)

        // 如果用户已登录，检查是否已点赞
        if (isUserLoggedIn) {
          const { data: userVote } = await supabase
            .from('user_votes')
            .select('id')
            .eq('product_id', productId)
            .eq('user_id', session.user.id)
            .maybeSingle()

          setHasVoted(!!userVote)
        }
      } catch (error) {
        console.error("获取数据失败:", error)
      }
    }

    fetchData()
  }, [productId])

  const handleVote = async () => {
    if (isLoading) return
    setIsLoading(true)

    try {
      // If user is not logged in, prompt to login
      if (!isLoggedIn) {
        toast({
          title: "Please log in",
          description: "You need to be logged in to upvote products",
          variant: "destructive"
        })
        setIsLoading(false)
        return
      }

      const supabase = createSupabaseClient()

      if (hasVoted) {
        // 取消点赞
        const { error: deleteError } = await supabase
          .from('user_votes')
          .delete()
          .eq('product_id', productId)
          .eq('user_id', (await supabase.auth.getUser()).data.user?.id || '')

        if (deleteError) {
          throw deleteError
        }

        setUpvotes((prev) => Math.max(0, prev - 1))
        setHasVoted(false)
        toast({
          title: "Upvote removed",
          description: "You have successfully removed your upvote",
        })
      } else {
        // 先检查产品是否存在于 product_votes 表中
        const { data: existingVote } = await supabase
          .from('product_votes')
          .select('product_id')
          .eq('product_id', productId)
          .maybeSingle()

        // 如果产品不存在于 product_votes 表中，则创建一个新记录
        if (!existingVote) {
          await supabase
            .from('product_votes')
            .insert({
              product_id: productId,
              upvotes: 0
            })
        }

        // 添加点赞
        const { error: insertError } = await supabase
          .from('user_votes')
          .insert({
            product_id: productId,
            user_id: (await supabase.auth.getUser()).data.user?.id || ''
          })

        if (insertError) {
          throw insertError
        }

        setUpvotes((prev) => prev + 1)
        setHasVoted(true)
        toast({
          title: "Upvoted",
          description: "Thanks for your support!",
        })
      }
    } catch (error) {
      console.error("点赞操作失败:", error)
      toast({
        title: "Operation failed",
        description: "An error occurred, please try again later",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div
      role="button"
      tabIndex={isLoading ? -1 : 0}
      aria-pressed={hasVoted}
      aria-label={isLoggedIn ? (hasVoted ? "Remove upvote" : "Upvote product") : "Log in to upvote to vote"}
      title={isLoggedIn ? (hasVoted ? "Remove upvote" : "Upvote") : "Log in to upvote"}
      className={cn(
        "flex flex-col items-center justify-center border rounded-2xl h-[4.5rem] w-[4.5rem] overflow-hidden transition-all focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
        hasVoted ? "border-green-200 bg-green-50 text-green-700" : "border-gray-200 text-gray-700",
        isLoading
          ? "opacity-50 cursor-not-allowed"
          : "cursor-pointer hover:bg-gray-50 active:bg-gray-100",
        hasVoted && !isLoading && "hover:bg-green-100 active:bg-green-200",
        className
      )}
      onClick={!isLoading ? handleVote : undefined}
      onKeyDown={!isLoading ? (e) => { if (e.key === 'Enter' || e.key === ' ') { e.preventDefault(); handleVote(); } } : undefined}
    >
      <ChevronUp
        className={cn(
          "h-5 w-5 mb-1 transition-colors",
          hasVoted ? "text-green-600" : "text-muted-foreground"
        )}
      />
      <span
        className={cn(
          "text-sm font-medium transition-colors",
          hasVoted ? "text-green-700" : "text-muted-foreground"
        )}
      >
        {upvotes || 0}
      </span>
    </div>
  )
}
